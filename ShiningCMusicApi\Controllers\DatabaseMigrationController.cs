using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicApi.Services.DatabaseMigration.Interfaces;
using ShiningCMusicApi.Services.DatabaseMigration.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;

namespace ShiningCMusicApi.Controllers
{
    /// <summary>
    /// Controller for managing database migrations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize] // Require authentication for all migration operations
    public class DatabaseMigrationController : ControllerBase
    {
        private readonly IDatabaseMigrationService _migrationService;
        private readonly ILogger<DatabaseMigrationController> _logger;

        public DatabaseMigrationController(
            IDatabaseMigrationService migrationService, 
            ILogger<DatabaseMigrationController> logger)
        {
            _migrationService = migrationService;
            _logger = logger;
        }

        /// <summary>
        /// Apply all pending migrations to the database
        /// </summary>
        /// <returns>Result of the migration operation</returns>
        [HttpPost("apply-all")]
        public async Task<ActionResult<MigrationResult>> ApplyAllMigrations()
        {
            try
            {
                _logger.LogInformation("API request to apply all migrations");
                var result = await _migrationService.ApplyAllMigrationsAsync();
                
                if (result.Success)
                {
                    return Ok(result);
                }
                
                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to apply migrations via API");
                return StatusCode(500, new { 
                    message = "Migration failed", 
                    error = ex.Message,
                    success = false
                });
            }
        }

        /// <summary>
        /// Get the status of all migration scripts
        /// </summary>
        /// <returns>List of migration statuses</returns>
        [HttpGet("status")]
        public async Task<ActionResult<List<MigrationStatus>>> GetMigrationStatus()
        {
            try
            {
                var status = await _migrationService.GetMigrationStatusAsync();
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get migration status via API");
                return StatusCode(500, new { 
                    message = "Failed to get migration status", 
                    error = ex.Message 
                });
            }
        }

        /// <summary>
        /// Apply a specific migration script
        /// </summary>
        /// <param name="scriptName">Name of the script to apply</param>
        /// <returns>Result of the migration operation</returns>
        [HttpPost("apply/{scriptName}")]
        public async Task<ActionResult<MigrationResult>> ApplySpecificMigration(
            [Required] string scriptName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(scriptName))
                {
                    return BadRequest(new { message = "Script name is required" });
                }

                _logger.LogInformation($"API request to apply specific migration: {scriptName}");
                var result = await _migrationService.ApplySpecificMigrationAsync(scriptName);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                
                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to apply migration {scriptName} via API");
                return StatusCode(500, new { 
                    message = $"Failed to apply migration {scriptName}", 
                    error = ex.Message,
                    success = false
                });
            }
        }

        /// <summary>
        /// Force apply a specific migration script (removes existing record first)
        /// </summary>
        /// <param name="scriptName">Name of the script to force apply</param>
        /// <returns>Result of the migration operation</returns>
        [HttpPost("force-apply/{scriptName}")]
        public async Task<ActionResult<MigrationResult>> ForceApplyMigration(
            [Required] string scriptName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(scriptName))
                {
                    return BadRequest(new { message = "Script name is required" });
                }

                _logger.LogInformation($"API request to force apply migration: {scriptName}");
                var result = await _migrationService.ForceApplyMigrationAsync(scriptName);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                
                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to force apply migration {scriptName} via API");
                return StatusCode(500, new { 
                    message = $"Failed to force apply migration {scriptName}", 
                    error = ex.Message,
                    success = false
                });
            }
        }

        /// <summary>
        /// Rollback a specific migration (if rollback script exists)
        /// </summary>
        /// <param name="scriptName">Name of the script to rollback</param>
        /// <returns>Result of the rollback operation</returns>
        [HttpPost("rollback/{scriptName}")]
        public async Task<ActionResult<MigrationResult>> RollbackMigration(
            [Required] string scriptName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(scriptName))
                {
                    return BadRequest(new { message = "Script name is required" });
                }

                _logger.LogInformation($"API request to rollback migration: {scriptName}");
                var result = await _migrationService.RollbackMigrationAsync(scriptName);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                
                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to rollback migration {scriptName} via API");
                return StatusCode(500, new { 
                    message = $"Failed to rollback migration {scriptName}", 
                    error = ex.Message,
                    success = false
                });
            }
        }

        /// <summary>
        /// Get detailed information about a specific migration
        /// </summary>
        /// <param name="scriptName">Name of the script</param>
        /// <returns>Migration status with detailed information</returns>
        [HttpGet("details/{scriptName}")]
        public async Task<ActionResult<MigrationStatus>> GetMigrationDetails(
            [Required] string scriptName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(scriptName))
                {
                    return BadRequest(new { message = "Script name is required" });
                }

                var details = await _migrationService.GetMigrationDetailsAsync(scriptName);
                
                if (details == null)
                {
                    return NotFound(new { message = $"Migration not found: {scriptName}" });
                }
                
                return Ok(details);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get migration details for {scriptName} via API");
                return StatusCode(500, new { 
                    message = $"Failed to get migration details for {scriptName}", 
                    error = ex.Message 
                });
            }
        }

        /// <summary>
        /// Discover all available SQL scripts
        /// </summary>
        /// <returns>List of discovered SQL scripts</returns>
        [HttpGet("scripts")]
        public async Task<ActionResult<List<SqlScript>>> DiscoverScripts()
        {
            try
            {
                var scripts = await _migrationService.DiscoverScriptsAsync();
                return Ok(scripts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to discover scripts via API");
                return StatusCode(500, new { 
                    message = "Failed to discover scripts", 
                    error = ex.Message 
                });
            }
        }

        /// <summary>
        /// Validate database connection
        /// </summary>
        /// <returns>Connection validation result</returns>
        [HttpGet("validate-connection")]
        public async Task<ActionResult<object>> ValidateConnection()
        {
            try
            {
                var isValid = await _migrationService.ValidateDatabaseConnectionAsync();
                return Ok(new { 
                    isValid = isValid,
                    message = isValid ? "Database connection is valid" : "Database connection failed"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to validate connection via API");
                return StatusCode(500, new { 
                    isValid = false,
                    message = "Connection validation failed", 
                    error = ex.Message 
                });
            }
        }

        /// <summary>
        /// Validate migration dependencies
        /// </summary>
        /// <returns>List of dependency validation issues</returns>
        [HttpGet("validate-dependencies")]
        public async Task<ActionResult<object>> ValidateDependencies()
        {
            try
            {
                var issues = await _migrationService.ValidateDependenciesAsync();
                return Ok(new { 
                    isValid = issues.Count == 0,
                    issues = issues,
                    message = issues.Count == 0 ? "All dependencies are satisfied" : $"Found {issues.Count} dependency issues"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to validate dependencies via API");
                return StatusCode(500, new { 
                    isValid = false,
                    message = "Dependency validation failed", 
                    error = ex.Message 
                });
            }
        }

        /// <summary>
        /// Create a database backup
        /// </summary>
        /// <returns>Backup creation result</returns>
        [HttpPost("backup")]
        public async Task<ActionResult<object>> CreateBackup()
        {
            try
            {
                _logger.LogInformation("API request to create database backup");
                var backupPath = await _migrationService.CreateDatabaseBackupAsync();
                
                if (backupPath != null)
                {
                    return Ok(new { 
                        success = true,
                        backupPath = backupPath,
                        message = "Database backup created successfully"
                    });
                }
                
                return BadRequest(new { 
                    success = false,
                    message = "Failed to create database backup"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create backup via API");
                return StatusCode(500, new { 
                    success = false,
                    message = "Backup creation failed", 
                    error = ex.Message 
                });
            }
        }

        /// <summary>
        /// Mark a migration as applied without executing it
        /// </summary>
        /// <param name="request">Mark as applied request</param>
        /// <returns>Operation result</returns>
        [HttpPost("mark-applied")]
        public async Task<ActionResult<object>> MarkAsApplied([FromBody] MarkAsAppliedRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.ScriptName))
                {
                    return BadRequest(new { message = "Script name is required" });
                }

                var appliedBy = request.AppliedBy ?? User.Identity?.Name ?? "API User";
                
                _logger.LogInformation($"API request to mark migration as applied: {request.ScriptName} by {appliedBy}");
                var success = await _migrationService.MarkMigrationAsAppliedAsync(request.ScriptName, appliedBy);
                
                if (success)
                {
                    return Ok(new { 
                        success = true,
                        message = $"Migration marked as applied: {request.ScriptName}"
                    });
                }
                
                return BadRequest(new { 
                    success = false,
                    message = $"Failed to mark migration as applied: {request.ScriptName}"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to mark migration as applied via API: {request.ScriptName}");
                return StatusCode(500, new { 
                    success = false,
                    message = "Failed to mark migration as applied", 
                    error = ex.Message 
                });
            }
        }

        /// <summary>
        /// Remove a migration record from the tracking table
        /// </summary>
        /// <param name="scriptName">Name of the script to remove</param>
        /// <returns>Operation result</returns>
        [HttpDelete("remove/{scriptName}")]
        public async Task<ActionResult<object>> RemoveMigrationRecord([Required] string scriptName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(scriptName))
                {
                    return BadRequest(new { message = "Script name is required" });
                }

                _logger.LogInformation($"API request to remove migration record: {scriptName}");
                var success = await _migrationService.RemoveMigrationRecordAsync(scriptName);
                
                if (success)
                {
                    return Ok(new { 
                        success = true,
                        message = $"Migration record removed: {scriptName}"
                    });
                }
                
                return NotFound(new { 
                    success = false,
                    message = $"Migration record not found: {scriptName}"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to remove migration record via API: {scriptName}");
                return StatusCode(500, new { 
                    success = false,
                    message = "Failed to remove migration record", 
                    error = ex.Message 
                });
            }
        }
    }

    /// <summary>
    /// Request model for marking a migration as applied
    /// </summary>
    public class MarkAsAppliedRequest
    {
        [Required]
        public string ScriptName { get; set; } = string.Empty;
        
        public string? AppliedBy { get; set; }
    }
}
