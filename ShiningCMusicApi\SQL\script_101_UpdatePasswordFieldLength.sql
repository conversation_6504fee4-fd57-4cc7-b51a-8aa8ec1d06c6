-- <PERSON><PERSON>t to update the Password field length to accommodate hashed passwords
-- BCrypt hashes are typically 60 characters long, so we're setting it to 100 for safety

-- Backup your database before running this script!

USE [MusicSchool]
GO

-- Update the Password column to allow longer hashed passwords
ALTER TABLE [dbo].[Users] 
ALTER COLUMN [Password] NVARCHAR(100) NULL;
GO

PRINT 'Password field length updated successfully to NVARCHAR(100)'
