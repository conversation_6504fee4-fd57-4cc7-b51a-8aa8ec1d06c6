using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ShiningCMusicApi.Services.DatabaseMigration.Interfaces;
using ShiningCMusicApi.Services.DatabaseMigration.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace ShiningCMusicApi.Services.DatabaseMigration
{
    /// <summary>
    /// Service for managing database migrations
    /// </summary>
    public class DatabaseMigrationService : IDatabaseMigrationService
    {
        private readonly string _connectionString;
        private readonly ILogger<DatabaseMigrationService> _logger;
        private readonly string _sqlScriptsPath;
        private readonly DatabaseMigrationOptions _options;

        public DatabaseMigrationService(IConfiguration configuration, ILogger<DatabaseMigrationService> logger)
        {
            _connectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING")
                ?? configuration.GetConnectionString("MusicSchool")
                ?? throw new InvalidOperationException("Database connection string is missing.");
            
            _logger = logger;
            _sqlScriptsPath = Path.Combine(Directory.GetCurrentDirectory(), "SQL");
            
            // Load configuration options
            _options = new DatabaseMigrationOptions();
            configuration.GetSection("DatabaseMigration").Bind(_options);
        }

        public async Task<MigrationResult> ApplyAllMigrationsAsync()
        {
            var result = new MigrationResult();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogInformation("Starting database migration process...");

                // 1. Validate connection
                if (!await ValidateDatabaseConnectionAsync())
                {
                    result.Success = false;
                    result.Message = "Database connection validation failed";
                    return result;
                }

                // 2. Ensure migration tracking table exists
                await EnsureMigrationTableExistsAsync();

                // 3. Discover and order scripts
                var scripts = await DiscoverScriptsAsync();
                var appliedMigrations = await GetAppliedMigrationsAsync();

                // 4. Filter scripts that need to be applied
                var scriptsToApply = scripts.Where(s =>
                    !appliedMigrations.ContainsKey(s.Name) ||
                    appliedMigrations[s.Name].ScriptHash != s.Hash
                ).OrderBy(s => s.ExecutionOrder).ToList();

                if (!scriptsToApply.Any())
                {
                    result.Success = true;
                    result.Message = "All migrations are up to date";
                    _logger.LogInformation("All migrations are up to date");
                    return result;
                }

                _logger.LogInformation($"Found {scriptsToApply.Count} migrations to apply");

                // 5. Validate dependencies
                var dependencyIssues = await ValidateDependenciesAsync();
                if (dependencyIssues.Any())
                {
                    result.Success = false;
                    result.Message = "Dependency validation failed";
                    result.Warnings.AddRange(dependencyIssues);
                    return result;
                }

                // 6. Create backup if configured
                if (_options.BackupBeforeMigration)
                {
                    var backupPath = await CreateDatabaseBackupAsync();
                    if (backupPath != null)
                    {
                        _logger.LogInformation($"Database backup created: {backupPath}");
                    }
                }

                // 7. Apply migrations in order
                foreach (var script in scriptsToApply)
                {
                    var scriptResult = await ApplyScriptAsync(script, appliedMigrations.ContainsKey(script.Name));
                    result.ScriptResults.Add(scriptResult);

                    if (scriptResult.Success)
                    {
                        result.AppliedScripts.Add(script.Name);
                        _logger.LogInformation($"Successfully applied migration: {script.Name} ({scriptResult.ExecutionTime.TotalMilliseconds:F0}ms)");
                    }
                    else
                    {
                        result.FailedScripts.Add(script.Name);
                        result.Warnings.Add($"{script.Name}: {scriptResult.ErrorMessage}");
                        _logger.LogError($"Failed to apply migration: {script.Name} - {scriptResult.ErrorMessage}");

                        // Decide whether to continue or stop on failure
                        if (script.IsCritical || _options.StopOnFirstFailure)
                        {
                            result.Success = false;
                            result.Message = $"Critical migration failed: {script.Name}";
                            return result;
                        }
                    }
                }

                result.Success = result.FailedScripts.Count == 0;
                result.Message = result.Success
                    ? $"Successfully applied {result.AppliedScripts.Count} migrations"
                    : $"Applied {result.AppliedScripts.Count} migrations with {result.FailedScripts.Count} failures";

                _logger.LogInformation(result.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Migration process failed");
                result.Success = false;
                result.Message = $"Migration process failed: {ex.Message}";
            }
            finally
            {
                stopwatch.Stop();
                result.TotalExecutionTime = stopwatch.Elapsed;
                _logger.LogInformation($"Migration process completed in {stopwatch.Elapsed.TotalSeconds:F2} seconds");
            }

            return result;
        }

        public async Task<bool> ValidateDatabaseConnectionAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                
                using var command = new SqlCommand("SELECT 1", connection);
                await command.ExecuteScalarAsync();
                
                _logger.LogDebug("Database connection validation successful");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database connection validation failed");
                return false;
            }
        }

        public async Task<List<SqlScript>> DiscoverScriptsAsync()
        {
            var scripts = new List<SqlScript>();

            if (!Directory.Exists(_sqlScriptsPath))
            {
                _logger.LogWarning($"SQL scripts directory not found: {_sqlScriptsPath}");
                return scripts;
            }

            var scriptFiles = Directory.GetFiles(_sqlScriptsPath, "*.sql")
                .Where(f => !Path.GetFileName(f).StartsWith("Rollback", StringComparison.OrdinalIgnoreCase))
                .Where(f => !Path.GetFileName(f).StartsWith("Test_", StringComparison.OrdinalIgnoreCase))
                .Where(f => !Path.GetFileName(f).StartsWith("Verify", StringComparison.OrdinalIgnoreCase))
                .ToList();

            foreach (var file in scriptFiles)
            {
                try
                {
                    var fileInfo = new FileInfo(file);
                    var content = await File.ReadAllTextAsync(file);
                    var fileName = Path.GetFileName(file);

                    var script = new SqlScript
                    {
                        Name = fileName,
                        Content = content,
                        Hash = ComputeHash(content),
                        ExecutionOrder = GetExecutionOrder(fileName),
                        FilePath = file,
                        FileSize = fileInfo.Length,
                        LastModified = fileInfo.LastWriteTime,
                        Category = DetermineScriptCategory(fileName),
                        IsCritical = IsCriticalScript(fileName),
                        CommandTimeoutSeconds = _options.CommandTimeoutSeconds,
                        Description = ExtractScriptDescription(content)
                    };

                    scripts.Add(script);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error processing script file: {file}");
                }
            }

            _logger.LogInformation($"Discovered {scripts.Count} migration scripts");
            return scripts.OrderBy(s => s.ExecutionOrder).ToList();
        }

        private async Task EnsureMigrationTableExistsAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                // Check if table exists
                var checkTableSql = @"
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_SCHEMA = 'dbo' 
                    AND TABLE_NAME = 'DatabaseMigrations'";

                using var checkCommand = new SqlCommand(checkTableSql, connection);
                var tableExists = (int)await checkCommand.ExecuteScalarAsync() > 0;

                if (!tableExists)
                {
                    _logger.LogInformation("Creating DatabaseMigrations table...");
                    
                    // Read and execute the migration table creation script
                    var migrationTableScript = Path.Combine(_sqlScriptsPath, "Add_DatabaseMigrations_Table.sql");
                    if (File.Exists(migrationTableScript))
                    {
                        var scriptContent = await File.ReadAllTextAsync(migrationTableScript);
                        var batches = SplitSqlScript(scriptContent);
                        
                        foreach (var batch in batches)
                        {
                            if (string.IsNullOrWhiteSpace(batch)) continue;
                            
                            using var command = new SqlCommand(batch, connection);
                            command.CommandTimeout = _options.CommandTimeoutSeconds;
                            await command.ExecuteNonQueryAsync();
                        }
                    }
                    else
                    {
                        // Create basic table if script doesn't exist
                        await CreateBasicMigrationTableAsync(connection);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to ensure migration table exists");
                throw;
            }
        }

        private async Task CreateBasicMigrationTableAsync(SqlConnection connection)
        {
            var createTableSql = @"
                CREATE TABLE [dbo].[DatabaseMigrations] (
                    [Id] INT IDENTITY(1,1) NOT NULL,
                    [ScriptName] NVARCHAR(255) NOT NULL,
                    [AppliedDate] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
                    [Status] NVARCHAR(50) NOT NULL DEFAULT 'Success',
                    [ExecutionTimeMs] BIGINT NULL,
                    [ErrorMessage] NVARCHAR(MAX) NULL,
                    [ScriptHash] NVARCHAR(64) NOT NULL,
                    [AppliedBy] NVARCHAR(100) NULL DEFAULT 'System',
                    [ScriptCategory] NVARCHAR(50) NULL,
                    [BatchesExecuted] INT NULL DEFAULT 0,
                    [IsRollback] BIT NOT NULL DEFAULT 0,
                    [RolledBackDate] DATETIME2 NULL,
                    [RolledBackBy] NVARCHAR(100) NULL,
                    [Dependencies] NVARCHAR(500) NULL,
                    [FileSize] BIGINT NULL,
                    [LastModified] DATETIME2 NULL,
                    [Notes] NVARCHAR(1000) NULL,

                    CONSTRAINT [PK_DatabaseMigrations] PRIMARY KEY CLUSTERED ([Id] ASC),
                    CONSTRAINT [UQ_DatabaseMigrations_ScriptName] UNIQUE NONCLUSTERED ([ScriptName] ASC)
                );

                CREATE NONCLUSTERED INDEX [IX_DatabaseMigrations_AppliedDate]
                ON [dbo].[DatabaseMigrations] ([AppliedDate] DESC);

                CREATE NONCLUSTERED INDEX [IX_DatabaseMigrations_Status]
                ON [dbo].[DatabaseMigrations] ([Status] ASC);

                CREATE NONCLUSTERED INDEX [IX_DatabaseMigrations_ScriptHash]
                ON [dbo].[DatabaseMigrations] ([ScriptHash] ASC);";

            using var command = new SqlCommand(createTableSql, connection);
            command.CommandTimeout = _options.CommandTimeoutSeconds;
            await command.ExecuteNonQueryAsync();

            _logger.LogInformation("Created basic DatabaseMigrations table");
        }

        private async Task<Dictionary<string, (string ScriptHash, DateTime AppliedDate)>> GetAppliedMigrationsAsync()
        {
            var appliedMigrations = new Dictionary<string, (string ScriptHash, DateTime AppliedDate)>();

            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var sql = @"
                    SELECT [ScriptName], [ScriptHash], [AppliedDate]
                    FROM [dbo].[DatabaseMigrations]
                    WHERE [Status] = 'Success' AND [RolledBackDate] IS NULL";

                using var command = new SqlCommand(sql, connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    var scriptName = reader.GetString("ScriptName");
                    var scriptHash = reader.GetString("ScriptHash");
                    var appliedDate = reader.GetDateTime("AppliedDate");

                    appliedMigrations[scriptName] = (scriptHash, appliedDate);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get applied migrations");
                throw;
            }

            return appliedMigrations;
        }

        private async Task<ScriptExecutionResult> ApplyScriptAsync(SqlScript script, bool isUpdate)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new ScriptExecutionResult
            {
                ScriptName = script.Name,
                IsUpdate = isUpdate
            };

            try
            {
                _logger.LogInformation($"Applying migration: {script.Name}");

                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                // Split script by GO statements and execute each batch
                var batches = SplitSqlScript(script.Content);
                result.BatchesExecuted = batches.Count;

                if (_options.UseTransactions && script.UseTransaction)
                {
                    using var transaction = connection.BeginTransaction();
                    try
                    {
                        foreach (var batch in batches)
                        {
                            if (string.IsNullOrWhiteSpace(batch)) continue;

                            using var command = new SqlCommand(batch, connection, transaction);
                            command.CommandTimeout = script.CommandTimeoutSeconds;
                            await command.ExecuteNonQueryAsync();
                        }

                        // Record successful migration
                        await RecordMigrationAsync(connection, transaction, script, "Success", stopwatch.ElapsedMilliseconds, null);

                        await transaction.CommitAsync();
                        result.Success = true;
                    }
                    catch (Exception ex)
                    {
                        await transaction.RollbackAsync();
                        await RecordMigrationAsync(connection, null, script, "Failed", stopwatch.ElapsedMilliseconds, ex.Message);
                        result.Success = false;
                        result.ErrorMessage = ex.Message;
                    }
                }
                else
                {
                    // Execute without transaction
                    try
                    {
                        foreach (var batch in batches)
                        {
                            if (string.IsNullOrWhiteSpace(batch)) continue;

                            using var command = new SqlCommand(batch, connection);
                            command.CommandTimeout = script.CommandTimeoutSeconds;
                            await command.ExecuteNonQueryAsync();
                        }

                        await RecordMigrationAsync(connection, null, script, "Success", stopwatch.ElapsedMilliseconds, null);
                        result.Success = true;
                    }
                    catch (Exception ex)
                    {
                        await RecordMigrationAsync(connection, null, script, "Failed", stopwatch.ElapsedMilliseconds, ex.Message);
                        result.Success = false;
                        result.ErrorMessage = ex.Message;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error applying migration: {script.Name}");
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        private async Task RecordMigrationAsync(SqlConnection connection, SqlTransaction? transaction, SqlScript script, string status, long executionTimeMs, string? errorMessage)
        {
            try
            {
                var sql = @"
                    IF EXISTS (SELECT 1 FROM [dbo].[DatabaseMigrations] WHERE [ScriptName] = @ScriptName)
                    BEGIN
                        UPDATE [dbo].[DatabaseMigrations]
                        SET [AppliedDate] = GETUTCDATE(),
                            [Status] = @Status,
                            [ExecutionTimeMs] = @ExecutionTimeMs,
                            [ErrorMessage] = @ErrorMessage,
                            [ScriptHash] = @ScriptHash,
                            [AppliedBy] = @AppliedBy,
                            [ScriptCategory] = @ScriptCategory,
                            [BatchesExecuted] = @BatchesExecuted,
                            [FileSize] = @FileSize,
                            [LastModified] = @LastModified,
                            [Notes] = @Notes
                        WHERE [ScriptName] = @ScriptName
                    END
                    ELSE
                    BEGIN
                        INSERT INTO [dbo].[DatabaseMigrations]
                        ([ScriptName], [Status], [ExecutionTimeMs], [ErrorMessage], [ScriptHash], [AppliedBy],
                         [ScriptCategory], [BatchesExecuted], [FileSize], [LastModified], [Notes])
                        VALUES
                        (@ScriptName, @Status, @ExecutionTimeMs, @ErrorMessage, @ScriptHash, @AppliedBy,
                         @ScriptCategory, @BatchesExecuted, @FileSize, @LastModified, @Notes)
                    END";

                using var command = new SqlCommand(sql, connection, transaction);
                command.Parameters.AddWithValue("@ScriptName", script.Name);
                command.Parameters.AddWithValue("@Status", status);
                command.Parameters.AddWithValue("@ExecutionTimeMs", executionTimeMs);
                command.Parameters.AddWithValue("@ErrorMessage", (object?)errorMessage ?? DBNull.Value);
                command.Parameters.AddWithValue("@ScriptHash", script.Hash);
                command.Parameters.AddWithValue("@AppliedBy", "DatabaseMigrationService");
                command.Parameters.AddWithValue("@ScriptCategory", script.Category.ToString());
                command.Parameters.AddWithValue("@BatchesExecuted", SplitSqlScript(script.Content).Count);
                command.Parameters.AddWithValue("@FileSize", script.FileSize);
                command.Parameters.AddWithValue("@LastModified", script.LastModified);
                command.Parameters.AddWithValue("@Notes", (object?)script.Description ?? DBNull.Value);

                await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to record migration: {script.Name}");
                // Don't throw here as it would rollback the actual migration
            }
        }

        private List<string> SplitSqlScript(string script)
        {
            var batches = new List<string>();

            // Split by GO statements (case insensitive, must be on its own line)
            var goPattern = @"^\s*GO\s*$";
            var lines = script.Split(new[] { '\r', '\n' }, StringSplitOptions.None);
            var currentBatch = new StringBuilder();

            foreach (var line in lines)
            {
                if (Regex.IsMatch(line, goPattern, RegexOptions.IgnoreCase))
                {
                    if (currentBatch.Length > 0)
                    {
                        batches.Add(currentBatch.ToString().Trim());
                        currentBatch.Clear();
                    }
                }
                else
                {
                    currentBatch.AppendLine(line);
                }
            }

            // Add the last batch if it has content
            if (currentBatch.Length > 0)
            {
                batches.Add(currentBatch.ToString().Trim());
            }

            return batches.Where(b => !string.IsNullOrWhiteSpace(b)).ToList();
        }

        private string ComputeHash(string content)
        {
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(content));
            return Convert.ToHexString(hash);
        }

        private int GetExecutionOrder(string fileName)
        {
            // Define execution order based on script types and names
            var orderMap = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase)
            {
                { "MusicSchoolDB Create.sql", 1 },
                { "MusicSchoolDB Objects.sql", 2 },
                { "Add_DatabaseMigrations_Table.sql", 5 },
                { "Add_OpenIddict_Tables.sql", 10 },
                { "Add_ClientSecrets_Table.sql", 11 },
                { "Migrate_Clients_To_OpenIddict.sql", 12 },
                { "Sample Data.sql", 1000 } // Always last
            };

            if (orderMap.ContainsKey(fileName))
                return orderMap[fileName];

            // Default ordering patterns
            if (fileName.StartsWith("Add_", StringComparison.OrdinalIgnoreCase))
                return 100;

            if (fileName.StartsWith("Update_", StringComparison.OrdinalIgnoreCase))
                return 200;

            if (fileName.StartsWith("Migrate_", StringComparison.OrdinalIgnoreCase))
                return 300;

            if (fileName.Contains("Config", StringComparison.OrdinalIgnoreCase))
                return 400;

            // Default ordering for other scripts
            return 500;
        }

        private ScriptCategory DetermineScriptCategory(string fileName)
        {
            if (fileName.Contains("Create", StringComparison.OrdinalIgnoreCase))
                return ScriptCategory.DatabaseCreation;

            if (fileName.StartsWith("Add_", StringComparison.OrdinalIgnoreCase))
                return ScriptCategory.Schema;

            if (fileName.StartsWith("Update_", StringComparison.OrdinalIgnoreCase))
                return ScriptCategory.Schema;

            if (fileName.Contains("Data", StringComparison.OrdinalIgnoreCase) ||
                fileName.Contains("Sample", StringComparison.OrdinalIgnoreCase))
                return ScriptCategory.Data;

            if (fileName.Contains("Index", StringComparison.OrdinalIgnoreCase))
                return ScriptCategory.Index;

            if (fileName.Contains("sp_", StringComparison.OrdinalIgnoreCase) ||
                fileName.Contains("Procedure", StringComparison.OrdinalIgnoreCase))
                return ScriptCategory.Procedures;

            if (fileName.Contains("Config", StringComparison.OrdinalIgnoreCase))
                return ScriptCategory.Configuration;

            if (fileName.Contains("Security", StringComparison.OrdinalIgnoreCase) ||
                fileName.Contains("User", StringComparison.OrdinalIgnoreCase))
                return ScriptCategory.Security;

            return ScriptCategory.Unknown;
        }

        private bool IsCriticalScript(string fileName)
        {
            var criticalScripts = new[]
            {
                "MusicSchoolDB Create.sql",
                "MusicSchoolDB Objects.sql",
                "Add_DatabaseMigrations_Table.sql",
                "Add_OpenIddict_Tables.sql"
            };

            return criticalScripts.Contains(fileName, StringComparer.OrdinalIgnoreCase);
        }

        private string? ExtractScriptDescription(string content)
        {
            // Look for description in comments at the top of the file
            var lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var line in lines.Take(20)) // Only check first 20 lines
            {
                var trimmed = line.Trim();
                if (trimmed.StartsWith("-- Purpose:", StringComparison.OrdinalIgnoreCase) ||
                    trimmed.StartsWith("Purpose:", StringComparison.OrdinalIgnoreCase))
                {
                    return trimmed.Substring(trimmed.IndexOf(':') + 1).Trim();
                }

                if (trimmed.StartsWith("-- DESCRIPTION:", StringComparison.OrdinalIgnoreCase) ||
                    trimmed.StartsWith("DESCRIPTION:", StringComparison.OrdinalIgnoreCase))
                {
                    return trimmed.Substring(trimmed.IndexOf(':') + 1).Trim();
                }
            }

            return null;
        }

        public async Task<MigrationResult> ApplySpecificMigrationAsync(string scriptName)
        {
            var result = new MigrationResult();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogInformation($"Applying specific migration: {scriptName}");

                if (!await ValidateDatabaseConnectionAsync())
                {
                    result.Success = false;
                    result.Message = "Database connection validation failed";
                    return result;
                }

                await EnsureMigrationTableExistsAsync();

                var scripts = await DiscoverScriptsAsync();
                var script = scripts.FirstOrDefault(s => s.Name.Equals(scriptName, StringComparison.OrdinalIgnoreCase));

                if (script == null)
                {
                    result.Success = false;
                    result.Message = $"Script not found: {scriptName}";
                    return result;
                }

                var appliedMigrations = await GetAppliedMigrationsAsync();
                var isUpdate = appliedMigrations.ContainsKey(script.Name);

                var scriptResult = await ApplyScriptAsync(script, isUpdate);
                result.ScriptResults.Add(scriptResult);

                if (scriptResult.Success)
                {
                    result.AppliedScripts.Add(script.Name);
                    result.Success = true;
                    result.Message = $"Successfully applied migration: {scriptName}";
                }
                else
                {
                    result.FailedScripts.Add(script.Name);
                    result.Success = false;
                    result.Message = $"Failed to apply migration: {scriptName} - {scriptResult.ErrorMessage}";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error applying specific migration: {scriptName}");
                result.Success = false;
                result.Message = $"Error applying migration: {ex.Message}";
            }
            finally
            {
                stopwatch.Stop();
                result.TotalExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        public async Task<List<MigrationStatus>> GetMigrationStatusAsync()
        {
            var statusList = new List<MigrationStatus>();

            try
            {
                await EnsureMigrationTableExistsAsync();

                var scripts = await DiscoverScriptsAsync();
                var appliedMigrations = await GetAppliedMigrationsAsync();

                // Get detailed migration records
                var migrationRecords = await GetMigrationRecordsAsync();

                foreach (var script in scripts)
                {
                    var isApplied = appliedMigrations.ContainsKey(script.Name);
                    var hasChanged = isApplied && appliedMigrations[script.Name].ScriptHash != script.Hash;
                    var record = migrationRecords.FirstOrDefault(r => r.ScriptName == script.Name);

                    var status = new MigrationStatus
                    {
                        ScriptName = script.Name,
                        IsApplied = isApplied,
                        AppliedDate = isApplied ? appliedMigrations[script.Name].AppliedDate : null,
                        Status = record?.Status ?? (isApplied ? "Success" : "Pending"),
                        HasChanged = hasChanged,
                        CurrentHash = script.Hash,
                        AppliedHash = isApplied ? appliedMigrations[script.Name].ScriptHash : null,
                        ExecutionOrder = script.ExecutionOrder,
                        IsRollback = script.IsRollback,
                        ExecutionTimeMs = record?.ExecutionTimeMs,
                        ErrorMessage = record?.ErrorMessage,
                        AppliedBy = record?.AppliedBy,
                        RequiresManualReview = hasChanged || (record?.Status == "Failed")
                    };

                    statusList.Add(status);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting migration status");
                throw;
            }

            return statusList.OrderBy(s => s.ExecutionOrder).ToList();
        }

        private async Task<List<MigrationRecord>> GetMigrationRecordsAsync()
        {
            var records = new List<MigrationRecord>();

            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var sql = @"
                    SELECT [ScriptName], [AppliedDate], [Status], [ExecutionTimeMs],
                           [ErrorMessage], [ScriptHash], [AppliedBy], [ScriptCategory],
                           [BatchesExecuted], [IsRollback], [RolledBackDate], [RolledBackBy]
                    FROM [dbo].[DatabaseMigrations]
                    ORDER BY [AppliedDate] DESC";

                using var command = new SqlCommand(sql, connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    records.Add(new MigrationRecord
                    {
                        ScriptName = reader.GetString("ScriptName"),
                        AppliedDate = reader.GetDateTime("AppliedDate"),
                        Status = reader.GetString("Status"),
                        ExecutionTimeMs = reader.IsDBNull("ExecutionTimeMs") ? null : reader.GetInt64("ExecutionTimeMs"),
                        ErrorMessage = reader.IsDBNull("ErrorMessage") ? null : reader.GetString("ErrorMessage"),
                        ScriptHash = reader.GetString("ScriptHash"),
                        AppliedBy = reader.IsDBNull("AppliedBy") ? null : reader.GetString("AppliedBy"),
                        ScriptCategory = reader.IsDBNull("ScriptCategory") ? null : reader.GetString("ScriptCategory"),
                        BatchesExecuted = reader.IsDBNull("BatchesExecuted") ? null : reader.GetInt32("BatchesExecuted"),
                        IsRollback = reader.GetBoolean("IsRollback"),
                        RolledBackDate = reader.IsDBNull("RolledBackDate") ? null : reader.GetDateTime("RolledBackDate"),
                        RolledBackBy = reader.IsDBNull("RolledBackBy") ? null : reader.GetString("RolledBackBy")
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get migration records");
                throw;
            }

            return records;
        }

        public async Task<MigrationResult> RollbackMigrationAsync(string scriptName)
        {
            var result = new MigrationResult();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogInformation($"Rolling back migration: {scriptName}");

                // Look for rollback script
                var rollbackScriptName = $"Rollback{scriptName}";
                var rollbackScriptPath = Path.Combine(_sqlScriptsPath, rollbackScriptName);

                if (!File.Exists(rollbackScriptPath))
                {
                    result.Success = false;
                    result.Message = $"Rollback script not found: {rollbackScriptName}";
                    return result;
                }

                var content = await File.ReadAllTextAsync(rollbackScriptPath);
                var rollbackScript = new SqlScript
                {
                    Name = rollbackScriptName,
                    Content = content,
                    Hash = ComputeHash(content),
                    IsRollback = true,
                    UseTransaction = true,
                    CommandTimeoutSeconds = _options.CommandTimeoutSeconds
                };

                var scriptResult = await ApplyScriptAsync(rollbackScript, false);
                result.ScriptResults.Add(scriptResult);

                if (scriptResult.Success)
                {
                    // Mark original migration as rolled back
                    await MarkMigrationAsRolledBackAsync(scriptName);

                    result.AppliedScripts.Add(rollbackScriptName);
                    result.Success = true;
                    result.Message = $"Successfully rolled back migration: {scriptName}";
                }
                else
                {
                    result.FailedScripts.Add(rollbackScriptName);
                    result.Success = false;
                    result.Message = $"Failed to rollback migration: {scriptName} - {scriptResult.ErrorMessage}";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error rolling back migration: {scriptName}");
                result.Success = false;
                result.Message = $"Error rolling back migration: {ex.Message}";
            }
            finally
            {
                stopwatch.Stop();
                result.TotalExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        private async Task MarkMigrationAsRolledBackAsync(string scriptName)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var sql = @"
                    UPDATE [dbo].[DatabaseMigrations]
                    SET [RolledBackDate] = GETUTCDATE(),
                        [RolledBackBy] = 'DatabaseMigrationService'
                    WHERE [ScriptName] = @ScriptName";

                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@ScriptName", scriptName);
                await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to mark migration as rolled back: {scriptName}");
            }
        }
    }

    /// <summary>
    /// Represents a migration record from the database
    /// </summary>
    internal class MigrationRecord
    {
        public string ScriptName { get; set; } = string.Empty;
        public DateTime AppliedDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public long? ExecutionTimeMs { get; set; }
        public string? ErrorMessage { get; set; }
        public string ScriptHash { get; set; } = string.Empty;
        public string? AppliedBy { get; set; }
        public string? ScriptCategory { get; set; }
        public int? BatchesExecuted { get; set; }
        public bool IsRollback { get; set; }
        public DateTime? RolledBackDate { get; set; }
        public string? RolledBackBy { get; set; }
    }

    // Additional interface methods implementation
    public partial class DatabaseMigrationService
    {
        public async Task<MigrationResult> ForceApplyMigrationAsync(string scriptName)
        {
            var result = new MigrationResult();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogInformation($"Force applying migration: {scriptName}");

                if (!await ValidateDatabaseConnectionAsync())
                {
                    result.Success = false;
                    result.Message = "Database connection validation failed";
                    return result;
                }

                await EnsureMigrationTableExistsAsync();

                var scripts = await DiscoverScriptsAsync();
                var script = scripts.FirstOrDefault(s => s.Name.Equals(scriptName, StringComparison.OrdinalIgnoreCase));

                if (script == null)
                {
                    result.Success = false;
                    result.Message = $"Script not found: {scriptName}";
                    return result;
                }

                // Remove existing migration record to force re-application
                await RemoveMigrationRecordAsync(scriptName);

                var scriptResult = await ApplyScriptAsync(script, false);
                result.ScriptResults.Add(scriptResult);

                if (scriptResult.Success)
                {
                    result.AppliedScripts.Add(script.Name);
                    result.Success = true;
                    result.Message = $"Successfully force applied migration: {scriptName}";
                }
                else
                {
                    result.FailedScripts.Add(script.Name);
                    result.Success = false;
                    result.Message = $"Failed to force apply migration: {scriptName} - {scriptResult.ErrorMessage}";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error force applying migration: {scriptName}");
                result.Success = false;
                result.Message = $"Error force applying migration: {ex.Message}";
            }
            finally
            {
                stopwatch.Stop();
                result.TotalExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        public async Task<MigrationStatus?> GetMigrationDetailsAsync(string scriptName)
        {
            try
            {
                var statusList = await GetMigrationStatusAsync();
                return statusList.FirstOrDefault(s => s.ScriptName.Equals(scriptName, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting migration details for: {scriptName}");
                return null;
            }
        }

        public async Task<List<string>> ValidateDependenciesAsync()
        {
            var issues = new List<string>();

            try
            {
                var scripts = await DiscoverScriptsAsync();
                var appliedMigrations = await GetAppliedMigrationsAsync();

                foreach (var script in scripts)
                {
                    foreach (var dependency in script.Dependencies)
                    {
                        if (!appliedMigrations.ContainsKey(dependency))
                        {
                            issues.Add($"Script '{script.Name}' depends on '{dependency}' which has not been applied");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating dependencies");
                issues.Add($"Error validating dependencies: {ex.Message}");
            }

            return issues;
        }

        public async Task<string?> CreateDatabaseBackupAsync()
        {
            try
            {
                if (!_options.BackupBeforeMigration)
                    return null;

                var backupDirectory = Path.Combine(Directory.GetCurrentDirectory(), _options.BackupDirectory);
                Directory.CreateDirectory(backupDirectory);

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"MusicSchool_Backup_{timestamp}.bak";
                var backupPath = Path.Combine(backupDirectory, backupFileName);

                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var backupSql = $@"
                    BACKUP DATABASE [MusicSchool]
                    TO DISK = '{backupPath}'
                    WITH FORMAT, INIT, COMPRESSION";

                using var command = new SqlCommand(backupSql, connection);
                command.CommandTimeout = 600; // 10 minutes for backup
                await command.ExecuteNonQueryAsync();

                _logger.LogInformation($"Database backup created: {backupPath}");
                return backupPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create database backup");
                return null;
            }
        }

        public async Task<bool> MarkMigrationAsAppliedAsync(string scriptName, string appliedBy)
        {
            try
            {
                var scripts = await DiscoverScriptsAsync();
                var script = scripts.FirstOrDefault(s => s.Name.Equals(scriptName, StringComparison.OrdinalIgnoreCase));

                if (script == null)
                {
                    _logger.LogWarning($"Script not found for marking as applied: {scriptName}");
                    return false;
                }

                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var sql = @"
                    INSERT INTO [dbo].[DatabaseMigrations]
                    ([ScriptName], [Status], [ExecutionTimeMs], [ScriptHash], [AppliedBy],
                     [ScriptCategory], [BatchesExecuted], [FileSize], [LastModified], [Notes])
                    VALUES
                    (@ScriptName, 'Success', 0, @ScriptHash, @AppliedBy,
                     @ScriptCategory, 0, @FileSize, @LastModified, 'Manually marked as applied')";

                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@ScriptName", script.Name);
                command.Parameters.AddWithValue("@ScriptHash", script.Hash);
                command.Parameters.AddWithValue("@AppliedBy", appliedBy);
                command.Parameters.AddWithValue("@ScriptCategory", script.Category.ToString());
                command.Parameters.AddWithValue("@FileSize", script.FileSize);
                command.Parameters.AddWithValue("@LastModified", script.LastModified);

                await command.ExecuteNonQueryAsync();

                _logger.LogInformation($"Marked migration as applied: {scriptName} by {appliedBy}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to mark migration as applied: {scriptName}");
                return false;
            }
        }

        public async Task<bool> RemoveMigrationRecordAsync(string scriptName)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var sql = "DELETE FROM [dbo].[DatabaseMigrations] WHERE [ScriptName] = @ScriptName";

                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@ScriptName", scriptName);

                var rowsAffected = await command.ExecuteNonQueryAsync();

                if (rowsAffected > 0)
                {
                    _logger.LogInformation($"Removed migration record: {scriptName}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to remove migration record: {scriptName}");
                return false;
            }
        }
    }

    /// <summary>
    /// Configuration options for database migrations
    /// </summary>
    public class DatabaseMigrationOptions
    {
        public bool RunOnStartup { get; set; } = false;
        public string SqlScriptsPath { get; set; } = "SQL";
        public bool BackupBeforeMigration { get; set; } = false;
        public bool StopOnFirstFailure { get; set; } = false;
        public int CommandTimeoutSeconds { get; set; } = 300;
        public bool UseTransactions { get; set; } = true;
        public string BackupDirectory { get; set; } = "Backups";
    }
}
