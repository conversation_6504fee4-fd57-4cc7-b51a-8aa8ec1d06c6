USE [MusicSchool]
GO

-- Check if the tables exist, if not create them
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[EmailTemplates]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[EmailTemplates](
        [Name] [nvarchar](50) NOT NULL,
        [CcEmailAddresses] [nvarchar](256) NULL,
        [BccEmailAddresses] [nvarchar](256) NULL,
        [Subject] [nvarchar](500) NULL,
        [BodyText] [nvarchar](max) NULL,
        [BodyHtml] [nvarchar](max) NULL,
     CONSTRAINT [PK_EmailTemplates] PRIMARY KEY CLUSTERED 
    (
        [Name] ASC
    )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
END
GO

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[EmailAttachments]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[EmailAttachments](
        [ID] [int] IDENTITY(1,1) NOT NULL,
        [TemplateName] [nvarchar](50) NOT NULL,
        [AttachmentName] [nvarchar](256) NOT NULL,
        [AttachmentPath] [nvarchar](512) NOT NULL,
     CONSTRAINT [PK_EmailAttachments] PRIMARY KEY CLUSTERED 
    (
        [ID] ASC
    )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
END
GO

-- Add foreign key constraint if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_EmailAttachments_EmailTemplates]') AND parent_object_id = OBJECT_ID(N'[dbo].[EmailAttachments]'))
BEGIN
    ALTER TABLE [dbo].[EmailAttachments]  WITH CHECK ADD  CONSTRAINT [FK_EmailAttachments_EmailTemplates] FOREIGN KEY([TemplateName])
    REFERENCES [dbo].[EmailTemplates] ([Name])
    ON DELETE CASCADE
    
    ALTER TABLE [dbo].[EmailAttachments] CHECK CONSTRAINT [FK_EmailAttachments_EmailTemplates]
END
GO

-- Insert or update the ScheduleReady template
IF EXISTS (SELECT * FROM [dbo].[EmailTemplates] WHERE [Name] = 'ScheduleReady')
BEGIN
    UPDATE [dbo].[EmailTemplates]
    SET [Subject] = 'Your Schedule is Ready - Shining C Music Studio',
        [BodyHtml] = N'<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #007bff; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
        .content { background-color: #f8f9fa; padding: 30px; border-radius: 0 0 5px 5px; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        .button { display: inline-block; background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 Shining C Music Studio</h1>
        </div>
        <div class="content">
            <h2>Hello {TutorName}!</h2>
            <p>We hope this email finds you well.</p>
            <p>We wanted to let you know that your teaching schedule has been updated and is now ready for review.</p>
            <p>Please <a href="https://shiningcmusicapp.azurewebsites.net/login">log in</a> to the Shining C Music Studio portal to view your latest schedule and any upcoming lessons.</p>
            <p>If you have any questions or concerns about your schedule, please don''t hesitate to contact us.</p>
            <p>Thank you for being a valued member of our teaching team!</p>
            <p>Best regards,<br>
            <strong>Shining C Music Studio Team</strong></p>
        </div>
        <div class="footer">
            <p>This is an automated message from Shining C Music Studio.<br>
            Please do not reply to this email.</p>
        </div>
    </div>
</body>
</html>',
        [BodyText] = NULL
    WHERE [Name] = 'ScheduleReady'
END
ELSE
BEGIN
    INSERT INTO [dbo].[EmailTemplates] ([Name], [Subject], [BodyHtml], [BodyText])
    VALUES ('ScheduleReady', 
            'Your Schedule is Ready - Shining C Music Studio',
            N'<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #007bff; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
        .content { background-color: #f8f9fa; padding: 30px; border-radius: 0 0 5px 5px; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        .button { display: inline-block; background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 Shining C Music Studio</h1>
        </div>
        <div class="content">
            <h2>Hello {TutorName}!</h2>
            <p>We hope this email finds you well.</p>
            <p>We wanted to let you know that your teaching schedule has been updated and is now ready for review.</p>
            <p>Please <a href="https://shiningcmusicapp.azurewebsites.net/login">log in</a> to the Shining C Music Studio portal to view your latest schedule and any upcoming lessons.</p>
            <p>If you have any questions or concerns about your schedule, please don''t hesitate to contact us.</p>
            <p>Thank you for being a valued member of our teaching team!</p>
            <p>Best regards,<br>
            <strong>Shining C Music Studio Team</strong></p>
        </div>
        <div class="footer">
            <p>This is an automated message from Shining C Music Studio.<br>
            Please do not reply to this email.</p>
        </div>
    </div>
</body>
</html>', NULL)
END
GO

PRINT 'Email templates setup completed successfully.'
GO
