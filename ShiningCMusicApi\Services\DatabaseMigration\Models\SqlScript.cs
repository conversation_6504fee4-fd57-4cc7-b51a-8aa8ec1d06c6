using System;
using System.Collections.Generic;

namespace ShiningCMusicApi.Services.DatabaseMigration.Models
{
    /// <summary>
    /// Represents a SQL migration script
    /// </summary>
    public class SqlScript
    {
        /// <summary>
        /// Name of the script file
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Full content of the SQL script
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// SHA256 hash of the script content
        /// </summary>
        public string Hash { get; set; } = string.Empty;

        /// <summary>
        /// Execution order for this script (lower numbers execute first)
        /// </summary>
        public int ExecutionOrder { get; set; }

        /// <summary>
        /// Whether this is a rollback script
        /// </summary>
        public bool IsRollback { get; set; }

        /// <summary>
        /// Scripts that must be executed before this one
        /// </summary>
        public List<string> Dependencies { get; set; } = new();

        /// <summary>
        /// Full file path to the script
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// File size in bytes
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// Last modified date of the script file
        /// </summary>
        public DateTime LastModified { get; set; }

        /// <summary>
        /// Whether this script should be executed in a transaction
        /// </summary>
        public bool UseTransaction { get; set; } = true;

        /// <summary>
        /// Command timeout in seconds for this script
        /// </summary>
        public int CommandTimeoutSeconds { get; set; } = 300;

        /// <summary>
        /// Whether this script is critical (failure should stop migration process)
        /// </summary>
        public bool IsCritical { get; set; }

        /// <summary>
        /// Category of the script (Schema, Data, Index, etc.)
        /// </summary>
        public ScriptCategory Category { get; set; }

        /// <summary>
        /// Description or purpose of the script
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// Categories for SQL scripts
    /// </summary>
    public enum ScriptCategory
    {
        /// <summary>
        /// Database creation scripts
        /// </summary>
        DatabaseCreation,

        /// <summary>
        /// Schema changes (tables, columns, constraints)
        /// </summary>
        Schema,

        /// <summary>
        /// Data migration or seeding
        /// </summary>
        Data,

        /// <summary>
        /// Index creation or modification
        /// </summary>
        Index,

        /// <summary>
        /// Stored procedures, functions, views
        /// </summary>
        Procedures,

        /// <summary>
        /// Configuration or settings
        /// </summary>
        Configuration,

        /// <summary>
        /// Security related changes
        /// </summary>
        Security,

        /// <summary>
        /// Performance optimizations
        /// </summary>
        Performance,

        /// <summary>
        /// Cleanup or maintenance
        /// </summary>
        Maintenance,

        /// <summary>
        /// Unknown or unspecified category
        /// </summary>
        Unknown
    }
}
