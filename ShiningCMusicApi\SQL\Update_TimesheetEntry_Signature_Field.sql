USE [MusicSchool]
GO

-- Add new Signature64 field to TimesheetEntries table for digital signature storage
-- This allows storing base64 encoded signature image data while keeping existing Signature field unchanged

PRINT 'Starting TimesheetEntry Signature64 field addition...'

-- Check if the new Signature64 column already exists
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
               WHERE TABLE_NAME = 'TimesheetEntries'
               AND COLUMN_NAME = 'Signature64'
               AND TABLE_SCHEMA = 'dbo')
BEGIN
    PRINT 'Adding new Signature64 column...'

    -- Add the new Signature64 column
    ALTER TABLE [dbo].[TimesheetEntries]
    ADD [Signature64] NVARCHAR(MAX) NULL

    PRINT 'Signature64 field successfully added as NVARCHAR(MAX)'
END
ELSE
BEGIN
    PRINT 'Signature64 column already exists. No update needed.'
END

-- Verify the existing Signature column is still intact
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
           WHERE TABLE_NAME = 'TimesheetEntries'
           AND COLUMN_NAME = 'Signature'
           AND TABLE_SCHEMA = 'dbo')
BEGIN
    DECLARE @CurrentDataType NVARCHAR(50)
    SELECT @CurrentDataType = DATA_TYPE + '(' +
           CASE
               WHEN CHARACTER_MAXIMUM_LENGTH = -1 THEN 'MAX'
               WHEN CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN CAST(CHARACTER_MAXIMUM_LENGTH AS NVARCHAR(10))
               ELSE 'N/A'
           END + ')'
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'TimesheetEntries'
    AND COLUMN_NAME = 'Signature'
    AND TABLE_SCHEMA = 'dbo'

    PRINT 'Existing Signature field data type: ' + @CurrentDataType + ' (unchanged)'
END

PRINT 'TimesheetEntry Signature64 field addition completed.'
GO
