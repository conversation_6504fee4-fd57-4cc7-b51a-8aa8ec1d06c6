# Database Migration System - Deployment Checklist

## 🚀 Initial Setup Checklist

### Prerequisites
- [ ] .NET 8.0 SDK installed
- [ ] SQL Server accessible
- [ ] Database connection string configured
- [ ] Authentication system working
- [ ] API endpoints accessible

### Setup Steps
- [ ] **Step 1**: Run migration tracking table setup
  ```bash
  sqlcmd -S your-server -d MusicSchool -i "SQL/Add_DatabaseMigrations_Table.sql"
  ```
- [ ] **Step 2**: Verify migration service registration in `Program.cs`
- [ ] **Step 3**: Configure `appsettings.json` with migration options
- [ ] **Step 4**: Test database connection via API
  ```bash
  curl -X GET "https://localhost:7091/api/databasemigration/validate-connection"
  ```
- [ ] **Step 5**: Discover existing scripts
  ```bash
  curl -X GET "https://localhost:7091/api/databasemigration/scripts"
  ```

## 🔧 Development Environment Setup

### Configuration
- [ ] Set `RunOnStartup: false` in development
- [ ] Enable detailed logging
- [ ] Set shorter command timeouts for testing
- [ ] Use local database for testing

### Testing
- [ ] Create test migration script
- [ ] Test script application via API
- [ ] Test rollback functionality
- [ ] Verify error handling
- [ ] Test dependency validation

## 🏗️ Staging Environment Deployment

### Pre-Deployment
- [ ] **Database Backup**: Create full backup of staging database
- [ ] **Script Review**: Review all new migration scripts
- [ ] **Dependency Check**: Validate script dependencies
  ```bash
  curl -X GET "https://localhost:7091/api/databasemigration/validate-dependencies"
  ```
- [ ] **Rollback Scripts**: Ensure rollback scripts exist for critical changes

### Deployment Steps
- [ ] **Step 1**: Deploy application code
- [ ] **Step 2**: Verify API endpoints are accessible
- [ ] **Step 3**: Check migration status
  ```bash
  curl -X GET "https://localhost:7091/api/databasemigration/status"
  ```
- [ ] **Step 4**: Apply migrations
  ```bash
  curl -X POST "https://localhost:7091/api/databasemigration/apply-all"
  ```
- [ ] **Step 5**: Verify migration results
- [ ] **Step 6**: Test application functionality

### Post-Deployment Verification
- [ ] All migrations applied successfully
- [ ] No failed migrations in status
- [ ] Application functions correctly
- [ ] Performance is acceptable
- [ ] Logs show no errors

## 🚀 Production Environment Deployment

### Pre-Deployment Checklist
- [ ] **Staging Success**: All tests passed in staging
- [ ] **Backup Strategy**: Full database backup plan ready
- [ ] **Rollback Plan**: Detailed rollback procedures documented
- [ ] **Maintenance Window**: Scheduled maintenance window
- [ ] **Team Notification**: All stakeholders notified
- [ ] **Monitoring**: Enhanced monitoring enabled

### Critical Configuration
- [ ] Set `RunOnStartup: false` (manual control recommended)
- [ ] Set `BackupBeforeMigration: true`
- [ ] Set `StopOnFirstFailure: true`
- [ ] Increase `CommandTimeoutSeconds` for large scripts
- [ ] Configure appropriate backup directory

### Deployment Process
- [ ] **Step 1**: Enable maintenance mode
- [ ] **Step 2**: Create database backup
  ```bash
  curl -X POST "https://localhost:7091/api/databasemigration/backup"
  ```
- [ ] **Step 3**: Deploy application code
- [ ] **Step 4**: Verify API accessibility
- [ ] **Step 5**: Validate environment
  ```bash
  curl -X GET "https://localhost:7091/api/databasemigration/validate-connection"
  curl -X GET "https://localhost:7091/api/databasemigration/validate-dependencies"
  ```
- [ ] **Step 6**: Review pending migrations
  ```bash
  curl -X GET "https://localhost:7091/api/databasemigration/status"
  ```
- [ ] **Step 7**: Apply migrations (with monitoring)
  ```bash
  curl -X POST "https://localhost:7091/api/databasemigration/apply-all"
  ```
- [ ] **Step 8**: Verify migration success
- [ ] **Step 9**: Test critical application functions
- [ ] **Step 10**: Disable maintenance mode

### Post-Deployment Verification
- [ ] All migrations show "Success" status
- [ ] No error messages in logs
- [ ] Application performance is normal
- [ ] All features working correctly
- [ ] Database integrity checks pass
- [ ] Monitoring shows normal metrics

## 🔄 Rollback Procedures

### Automatic Rollback (if rollback scripts exist)
- [ ] **Step 1**: Identify failed migration
- [ ] **Step 2**: Execute rollback
  ```bash
  curl -X POST "https://localhost:7091/api/databasemigration/rollback/FailedScript.sql"
  ```
- [ ] **Step 3**: Verify rollback success
- [ ] **Step 4**: Test application functionality

### Manual Rollback (restore from backup)
- [ ] **Step 1**: Stop application
- [ ] **Step 2**: Restore database from backup
- [ ] **Step 3**: Deploy previous application version
- [ ] **Step 4**: Verify system functionality
- [ ] **Step 5**: Update migration tracking if needed

## 📊 Monitoring and Maintenance

### Daily Monitoring
- [ ] Check for failed migrations
  ```sql
  SELECT * FROM DatabaseMigrations WHERE Status = 'Failed'
  ```
- [ ] Monitor migration execution times
- [ ] Review application logs for migration-related errors
- [ ] Verify backup processes are working

### Weekly Maintenance
- [ ] Review migration performance trends
- [ ] Clean up old backup files
- [ ] Update documentation for new migrations
- [ ] Review and optimize slow migrations

### Monthly Review
- [ ] Analyze migration patterns and frequency
- [ ] Review and update rollback procedures
- [ ] Assess backup and recovery strategies
- [ ] Update deployment procedures based on lessons learned

## 🚨 Emergency Procedures

### Migration Failure During Deployment
1. **Immediate Actions**:
   - [ ] Stop further migrations
   - [ ] Assess impact on application
   - [ ] Check error details in migration logs

2. **Recovery Options**:
   - [ ] **Option A**: Fix script and retry
   - [ ] **Option B**: Rollback specific migration
   - [ ] **Option C**: Restore from backup

3. **Communication**:
   - [ ] Notify stakeholders of issue
   - [ ] Provide estimated resolution time
   - [ ] Update on progress regularly

### Database Corruption
1. **Assessment**:
   - [ ] Run database integrity checks
   - [ ] Identify scope of corruption
   - [ ] Determine if migration-related

2. **Recovery**:
   - [ ] Restore from most recent backup
   - [ ] Re-apply migrations if needed
   - [ ] Verify data integrity

## 📋 Script Development Guidelines

### Before Writing Scripts
- [ ] Understand the change requirements
- [ ] Plan the migration strategy
- [ ] Consider impact on existing data
- [ ] Design rollback approach

### Script Writing Checklist
- [ ] **Header**: Include descriptive header with purpose, author, date
- [ ] **Dependencies**: Document any script dependencies
- [ ] **Idempotent**: Use `IF NOT EXISTS` where appropriate
- [ ] **Error Handling**: Include proper error checking
- [ ] **Verification**: Add queries to verify changes
- [ ] **Performance**: Consider impact on large tables
- [ ] **Rollback**: Create corresponding rollback script

### Testing Checklist
- [ ] Test on development database
- [ ] Test with sample data
- [ ] Test rollback script
- [ ] Verify performance impact
- [ ] Test in staging environment

## 🔐 Security Checklist

### Access Control
- [ ] Migration API requires authentication
- [ ] Only authorized users can execute migrations
- [ ] Audit trail captures who executed migrations
- [ ] Database credentials are secure

### Script Security
- [ ] Review scripts for SQL injection risks
- [ ] Validate input parameters
- [ ] Use parameterized queries where applicable
- [ ] Avoid dynamic SQL construction

## 📞 Support and Troubleshooting

### Common Issues and Solutions

**Issue**: Connection timeout during migration
- [ ] **Solution**: Increase `CommandTimeoutSeconds`
- [ ] **Prevention**: Test with production data volumes

**Issue**: Migration marked as failed but actually succeeded
- [ ] **Solution**: Use `mark-applied` endpoint to correct status
- [ ] **Prevention**: Improve error handling in scripts

**Issue**: Dependency validation fails
- [ ] **Solution**: Check script execution order
- [ ] **Prevention**: Document dependencies clearly

**Issue**: Rollback script doesn't exist
- [ ] **Solution**: Create rollback script or restore from backup
- [ ] **Prevention**: Always create rollback scripts

### Getting Help
- [ ] Check migration logs for detailed error messages
- [ ] Use validation endpoints to diagnose issues
- [ ] Review documentation for similar issues
- [ ] Contact development team with specific error details

---

## ✅ Final Deployment Sign-off

### Development Team Sign-off
- [ ] All migration scripts tested and reviewed
- [ ] Rollback procedures documented and tested
- [ ] Performance impact assessed
- [ ] Documentation updated

### Operations Team Sign-off
- [ ] Backup procedures verified
- [ ] Monitoring configured
- [ ] Rollback plan approved
- [ ] Maintenance window scheduled

### Business Team Sign-off
- [ ] Impact on users understood
- [ ] Communication plan ready
- [ ] Rollback criteria defined
- [ ] Go/no-go decision made

**Deployment Date**: _______________  
**Deployed By**: _______________  
**Verified By**: _______________  

---

**🎉 Deployment Complete!**

Remember to monitor the system closely for the first 24 hours after deployment and be ready to execute rollback procedures if needed.
