﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyTitle>Shining C Music Database Manager</AssemblyTitle>
    <AssemblyDescription>Standalone database migration manager for Shining C Music application</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.15" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.15" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.15" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.15" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.15" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.15" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.15" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="Spectre.Console" Version="0.48.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Content Include="SQL\**\*.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
