USE [MusicSchool]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[sp_GetStudents]
    @StudentId INT = NULL   -- optional: update/return one student
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    BEGIN TRY
        BEGIN TRAN;

        IF OBJECT_ID('tempdb..#Remaining') IS NOT NULL 
            DROP TABLE #Remaining;

        SELECT ts.StudentId, COUNT(*) AS RemainingLessons
        INTO #Remaining
        FROM dbo.Timesheets ts
        INNER JOIN dbo.TimesheetEntries tse
            ON tse.TimesheetId = ts.TimesheetId
        WHERE ts.IsArchived = 0
          AND tse.IsPresent = 0
          AND (@StudentId IS NULL OR ts.StudentId = @StudentId)
        GROUP BY ts.StudentId;

        UPDATE s
        SET s.RemainingLessons = ISNULL(r.<PERSON>maining<PERSON><PERSON><PERSON>, 0), UpdatedUTC = GETUTCDATE()
        FROM dbo.Students s
        LEFT JOIN #Remaining r
            ON r.StudentId = s.StudentId
        WHERE s.IsArchived = 0
        AND s.RemainingLessons <> ISNULL(r.RemainingLessons, 0)
        AND (@StudentId IS NULL OR s.StudentId = @StudentId);

        SELECT *
        FROM dbo.Students
        WHERE IsArchived = 0
        AND (@StudentId IS NULL OR StudentId = @StudentId)
        ORDER BY StudentId;

        COMMIT TRAN;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0 ROLLBACK TRAN;
        THROW;
    END CATCH
END
GO
