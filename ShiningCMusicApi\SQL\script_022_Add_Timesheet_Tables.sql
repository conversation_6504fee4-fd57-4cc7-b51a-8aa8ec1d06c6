USE [MusicSchool]
GO

-- Create Timesheets table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Timesheets' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Timesheets](
        [TimesheetId] [int] IDENTITY(1,1) NOT NULL,
        [StudentId] [int] NOT NULL,
        [TutorId] [int] NOT NULL,
        [SubjectId] [int] NOT NULL,
        [StartDate] [datetime] NOT NULL,
        [ContactNumber] [nvarchar](20) NULL,
        [ClassDurationMinutes] [int] NOT NULL,
        [CreatedUTC] [datetime] NOT NULL,
        [UpdatedUTC] [datetime] NULL,
        [IsArchived] [bit] NOT NULL,
        [Notes] [nvarchar](500) NULL,
    PRIMARY KEY CLUSTERED 
    (
        [TimesheetId] ASC
    )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]

    -- Add default constraints
    ALTER TABLE [dbo].[Timesheets] ADD CONSTRAINT [DF_Timesheets_CreatedUTC] DEFAULT (getutcdate()) FOR [CreatedUTC]
    ALTER TABLE [dbo].[Timesheets] ADD CONSTRAINT [DF_Timesheets_IsArchived] DEFAULT ((0)) FOR [IsArchived]

    -- Add foreign key constraints
    ALTER TABLE [dbo].[Timesheets] WITH CHECK ADD CONSTRAINT [FK_Timesheets_Students] FOREIGN KEY([StudentId])
    REFERENCES [dbo].[Students] ([StudentId])

    ALTER TABLE [dbo].[Timesheets] WITH CHECK ADD CONSTRAINT [FK_Timesheets_Tutors] FOREIGN KEY([TutorId])
    REFERENCES [dbo].[Tutors] ([TutorId])

    ALTER TABLE [dbo].[Timesheets] WITH CHECK ADD CONSTRAINT [FK_Timesheets_Subjects] FOREIGN KEY([SubjectId])
    REFERENCES [dbo].[Subjects] ([SubjectId])

    ALTER TABLE [dbo].[Timesheets] CHECK CONSTRAINT [FK_Timesheets_Students]
    ALTER TABLE [dbo].[Timesheets] CHECK CONSTRAINT [FK_Timesheets_Tutors]
    ALTER TABLE [dbo].[Timesheets] CHECK CONSTRAINT [FK_Timesheets_Subjects]

    PRINT 'Timesheets table created successfully'
END
ELSE
BEGIN
    PRINT 'Timesheets table already exists'
END
GO

-- Create TimesheetEntries table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='TimesheetEntries' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[TimesheetEntries](
        [TimesheetEntryId] [int] IDENTITY(1,1) NOT NULL,
        [TimesheetId] [int] NOT NULL,
        [AttendanceDateTime] [datetime] NOT NULL,
        [Signature] [nvarchar](100) NULL,
        [IsPresent] [bit] NOT NULL,
        [Notes] [nvarchar](200) NULL,
        [CreatedUTC] [datetime] NOT NULL,
        [UpdatedUTC] [datetime] NULL,
    PRIMARY KEY CLUSTERED 
    (
        [TimesheetEntryId] ASC
    )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]

    -- Add default constraints
    ALTER TABLE [dbo].[TimesheetEntries] ADD CONSTRAINT [DF_TimesheetEntries_CreatedUTC] DEFAULT (getutcdate()) FOR [CreatedUTC]
    ALTER TABLE [dbo].[TimesheetEntries] ADD CONSTRAINT [DF_TimesheetEntries_IsPresent] DEFAULT ((1)) FOR [IsPresent]

    -- Add foreign key constraint
    ALTER TABLE [dbo].[TimesheetEntries] WITH CHECK ADD CONSTRAINT [FK_TimesheetEntries_Timesheets] FOREIGN KEY([TimesheetId])
    REFERENCES [dbo].[Timesheets] ([TimesheetId])
    ON DELETE CASCADE

    ALTER TABLE [dbo].[TimesheetEntries] CHECK CONSTRAINT [FK_TimesheetEntries_Timesheets]

    PRINT 'TimesheetEntries table created successfully'
END
ELSE
BEGIN
    PRINT 'TimesheetEntries table already exists'
END
GO

-- Create indexes for better performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Timesheets_StudentId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Timesheets_StudentId] ON [dbo].[Timesheets]
    (
        [StudentId] ASC
    )
    PRINT 'Index IX_Timesheets_StudentId created'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Timesheets_TutorId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Timesheets_TutorId] ON [dbo].[Timesheets]
    (
        [TutorId] ASC
    )
    PRINT 'Index IX_Timesheets_TutorId created'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_TimesheetEntries_TimesheetId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_TimesheetEntries_TimesheetId] ON [dbo].[TimesheetEntries]
    (
        [TimesheetId] ASC
    )
    PRINT 'Index IX_TimesheetEntries_TimesheetId created'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_TimesheetEntries_AttendanceDate')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_TimesheetEntries_AttendanceDate] ON [dbo].[TimesheetEntries]
    (
        [AttendanceDateTime] ASC
    )
    PRINT 'Index IX_TimesheetEntries_AttendanceDate created'
END

PRINT 'Timesheet tables and indexes setup completed successfully'
GO
