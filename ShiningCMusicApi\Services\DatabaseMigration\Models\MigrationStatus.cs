using System;

namespace ShiningCMusicApi.Services.DatabaseMigration.Models
{
    /// <summary>
    /// Represents the status of a database migration script
    /// </summary>
    public class MigrationStatus
    {
        /// <summary>
        /// Name of the migration script
        /// </summary>
        public string ScriptName { get; set; } = string.Empty;

        /// <summary>
        /// Whether this script has been applied to the database
        /// </summary>
        public bool IsApplied { get; set; }

        /// <summary>
        /// Date and time when the script was applied (if applicable)
        /// </summary>
        public DateTime? AppliedDate { get; set; }

        /// <summary>
        /// Current status of the migration (Success, Failed, Pending, etc.)
        /// </summary>
        public string Status { get; set; } = "Pending";

        /// <summary>
        /// Whether the script content has changed since it was last applied
        /// </summary>
        public bool HasChanged { get; set; }

        /// <summary>
        /// SHA256 hash of the current script content
        /// </summary>
        public string CurrentHash { get; set; } = string.Empty;

        /// <summary>
        /// SHA256 hash of the script content when it was applied
        /// </summary>
        public string? AppliedHash { get; set; }

        /// <summary>
        /// Execution order for this script
        /// </summary>
        public int ExecutionOrder { get; set; }

        /// <summary>
        /// Whether this is a rollback script
        /// </summary>
        public bool IsRollback { get; set; }

        /// <summary>
        /// Time taken to execute this script (in milliseconds)
        /// </summary>
        public long? ExecutionTimeMs { get; set; }

        /// <summary>
        /// Error message if the script failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// User or system that applied this migration
        /// </summary>
        public string? AppliedBy { get; set; }

        /// <summary>
        /// Whether this script requires manual intervention
        /// </summary>
        public bool RequiresManualReview { get; set; }

        /// <summary>
        /// Dependencies that must be applied before this script
        /// </summary>
        public string? Dependencies { get; set; }
    }
}
