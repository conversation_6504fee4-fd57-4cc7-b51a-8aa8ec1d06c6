USE [MusicSchool]
GO

-- Add RemainingLessons field to Students table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Students]') AND name = 'RemainingLessons')
BEGIN
    ALTER TABLE [dbo].[Students]
    ADD [RemainingLessons] [int] NOT NULL DEFAULT 0
    
    PRINT 'RemainingLessons field added to Students table successfully.'
END
ELSE
BEGIN
    PRINT 'RemainingLessons field already exists in Students table.'
END
GO

-- Add RemainingLessons field to Tutors table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Tu<PERSON>]') AND name = 'RemainingLessons')
BEGIN
    ALTER TABLE [dbo].[Tutors]
    ADD [RemainingLessons] [int] NOT NULL DEFAULT 0
    
    PRINT 'RemainingLessons field added to Tutors table successfully.'
END
ELSE
BEGIN
    PRINT 'RemainingLessons field already exists in Tutors table.'
END
GO
