-- Create ClientSecrets table for storing plain text secrets
-- This table is separate from OpenIddict's hashed secrets for management purposes

USE [MusicSchool]
GO

-- Create ClientSecrets table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ClientSecrets' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ClientSecrets] (
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [ClientId] [nvarchar](100) NOT NULL,
        [PlainTextSecret] [nvarchar](500) NOT NULL,
        [CreatedAt] [datetime2](7) NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedAt] [datetime2](7) NULL,
        [Description] [nvarchar](200) NULL,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        [AccessTokenLifetimeSeconds] [int] NOT NULL DEFAULT 7200,
        CONSTRAINT [PK_ClientSecrets] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [UQ_ClientSecrets_ClientId] UNIQUE NONCLUSTERED ([ClientId] ASC)
    );

    CREATE NONCLUSTERED INDEX [IX_ClientSecrets_ClientId_IsActive] 
    ON [dbo].[ClientSecrets] ([ClientId] ASC, [IsActive] ASC);

    PRINT 'Created ClientSecrets table'
END
ELSE
BEGIN
    PRINT 'ClientSecrets table already exists'
END

-- Insert known client secrets
IF NOT EXISTS (SELECT 1 FROM [ClientSecrets] WHERE [ClientId] = 'wasm_client')
BEGIN
    INSERT INTO [ClientSecrets] ([ClientId], [PlainTextSecret], [Description])
    VALUES ('wasm_client', 'AE6qbzhQ08kW', 'Blazor WebAssembly Client')
    PRINT 'Added wasm_client secret'
END

IF NOT EXISTS (SELECT 1 FROM [ClientSecrets] WHERE [ClientId] = 'bff_client')
BEGIN
    INSERT INTO [ClientSecrets] ([ClientId], [PlainTextSecret], [Description])
    VALUES ('bff_client', 'sQt8WnEvV7oC', 'Bff Client')
    PRINT 'Added bff_client secret'
END

IF NOT EXISTS (SELECT 1 FROM [ClientSecrets] WHERE [ClientId] = 'admin')
BEGIN
    INSERT INTO [ClientSecrets] ([ClientId], [PlainTextSecret], [Description])
    VALUES ('admin', 'WpujhG4r3h0K', 'Admin Client')
    PRINT 'Added admin secret'
END

-- Verify the table
SELECT 
    [ClientId],
    [Description],
    [CreatedAt],
    [IsActive],
    LEN([PlainTextSecret]) as [SecretLength]
FROM [ClientSecrets]
WHERE [IsActive] = 1
ORDER BY [ClientId]

PRINT 'ClientSecrets table setup completed successfully!'
