USE [MusicSchool]
GO

-- Insert sample tutors
INSERT INTO [dbo].[Tu<PERSON>] ([<PERSON><PERSON><PERSON><PERSON>], [<PERSON><PERSON>], [SubjectId], [LoginName], [Color])
VALUES 
('<PERSON>', '<EMAIL>', 3, 'sue', '#FF6B6B'),
('<PERSON><PERSON><PERSON>', '<EMAIL>', 1, 'koeun', '#4ECDC4'),
('<PERSON><PERSON><PERSON>', '<EMAIL>', 1, 'kathrin', '#5742f5'),
('<PERSON>fellow', '<EMAIL>', 2, 'mitchell', '#1e88e5'),
('<PERSON>', '<EMAIL>', 1, 'christy', '#ab47bc')
GO

-- Insert sample students
INSERT INTO [dbo].[Students] ([StudentName], [<PERSON>ail], [SubjectId], [Tutor<PERSON>])
VALUES 
('<PERSON>', '<EMAIL>', 1, 1),
('<PERSON>', '<EMAIL>', 1, 1),
('<PERSON>', '<EMAIL>', 2, 2),
('<PERSON>', '<EMAIL>', 1, 2),
('<PERSON>', '<EMAIL>', 3, 3)
GO

-- Insert sample users
INSERT INTO [dbo].[Users] ([LoginName], [UserName], [Password], [RoleId])
VALUES 
('sue', 'Sue', 'password123', 2),
('koeun', 'Koeun', 'password123', 2),
('kathrin', 'Kathrin', 'password123', 2),
('mitchell', 'Mitchell', 'password123', 2),
('christy', 'Christy', 'password123', 1),
('admin', 'Admin', 'admin', 1)
GO
