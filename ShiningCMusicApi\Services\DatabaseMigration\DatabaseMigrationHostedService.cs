using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ShiningCMusicApi.Services.DatabaseMigration.Interfaces;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace ShiningCMusicApi.Services.DatabaseMigration
{
    /// <summary>
    /// Hosted service that can run database migrations on application startup
    /// </summary>
    public class DatabaseMigrationHostedService : IHostedService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<DatabaseMigrationHostedService> _logger;
        private readonly IConfiguration _configuration;
        private readonly DatabaseMigrationOptions _options;

        public DatabaseMigrationHostedService(
            IServiceProvider serviceProvider,
            ILogger<DatabaseMigrationHostedService> logger,
            IConfiguration configuration)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _configuration = configuration;
            
            _options = new DatabaseMigrationOptions();
            configuration.GetSection("DatabaseMigration").Bind(_options);
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            try
            {
                // Only run migrations if configured to do so
                if (!_options.RunOnStartup)
                {
                    _logger.LogInformation("Database migrations on startup is disabled");
                    return;
                }

                _logger.LogInformation("Starting database migrations on application startup...");

                using var scope = _serviceProvider.CreateScope();
                var migrationService = scope.ServiceProvider.GetRequiredService<IDatabaseMigrationService>();

                // First validate the database connection
                var connectionValid = await migrationService.ValidateDatabaseConnectionAsync();
                if (!connectionValid)
                {
                    _logger.LogError("Database connection validation failed during startup migrations");
                    
                    // Decide whether to throw exception or continue based on configuration
                    var failOnConnectionError = _configuration.GetValue<bool>("DatabaseMigration:FailOnConnectionError", true);
                    if (failOnConnectionError)
                    {
                        throw new InvalidOperationException("Database connection validation failed during startup");
                    }
                    
                    _logger.LogWarning("Continuing startup despite database connection failure");
                    return;
                }

                // Check if there are any pending migrations
                var migrationStatus = await migrationService.GetMigrationStatusAsync();
                var pendingMigrations = migrationStatus.Where(s => !s.IsApplied || s.HasChanged).ToList();

                if (!pendingMigrations.Any())
                {
                    _logger.LogInformation("No pending database migrations found");
                    return;
                }

                _logger.LogInformation($"Found {pendingMigrations.Count} pending migrations to apply on startup");

                // Apply all pending migrations
                var result = await migrationService.ApplyAllMigrationsAsync();

                if (result.Success)
                {
                    _logger.LogInformation($"Successfully applied {result.AppliedScripts.Count} migrations on startup in {result.TotalExecutionTime.TotalSeconds:F2} seconds");
                    
                    if (result.AppliedScripts.Any())
                    {
                        _logger.LogInformation($"Applied migrations: {string.Join(", ", result.AppliedScripts)}");
                    }
                }
                else
                {
                    _logger.LogError($"Database migrations failed on startup: {result.Message}");
                    
                    if (result.FailedScripts.Any())
                    {
                        _logger.LogError($"Failed migrations: {string.Join(", ", result.FailedScripts)}");
                    }

                    if (result.Warnings.Any())
                    {
                        foreach (var warning in result.Warnings)
                        {
                            _logger.LogWarning($"Migration warning: {warning}");
                        }
                    }

                    // Decide whether to throw exception or continue based on configuration
                    var failOnMigrationError = _configuration.GetValue<bool>("DatabaseMigration:FailOnMigrationError", false);
                    if (failOnMigrationError)
                    {
                        throw new InvalidOperationException($"Database migrations failed on startup: {result.Message}");
                    }
                    
                    _logger.LogWarning("Continuing startup despite migration failures");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during startup database migrations");
                
                // Decide whether to rethrow based on configuration
                var failOnError = _configuration.GetValue<bool>("DatabaseMigration:FailOnError", false);
                if (failOnError)
                {
                    throw;
                }
                
                _logger.LogWarning("Continuing startup despite migration error");
            }
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Database migration hosted service is stopping");
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// Extension methods for registering the database migration hosted service
    /// </summary>
    public static class DatabaseMigrationHostedServiceExtensions
    {
        /// <summary>
        /// Adds the database migration hosted service to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDatabaseMigrationHostedService(this IServiceCollection services)
        {
            services.AddHostedService<DatabaseMigrationHostedService>();
            return services;
        }

        /// <summary>
        /// Adds the database migration hosted service with configuration
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configureOptions">Action to configure migration options</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDatabaseMigrationHostedService(
            this IServiceCollection services, 
            Action<DatabaseMigrationOptions> configureOptions)
        {
            services.Configure(configureOptions);
            services.AddHostedService<DatabaseMigrationHostedService>();
            return services;
        }
    }

    /// <summary>
    /// Additional configuration options specific to the hosted service
    /// </summary>
    public class DatabaseMigrationHostedServiceOptions : DatabaseMigrationOptions
    {
        /// <summary>
        /// Whether to fail application startup if database connection validation fails
        /// </summary>
        public bool FailOnConnectionError { get; set; } = true;

        /// <summary>
        /// Whether to fail application startup if migrations fail
        /// </summary>
        public bool FailOnMigrationError { get; set; } = false;

        /// <summary>
        /// Whether to fail application startup on any migration-related error
        /// </summary>
        public bool FailOnError { get; set; } = false;

        /// <summary>
        /// Maximum time to wait for migrations to complete during startup (in seconds)
        /// </summary>
        public int StartupTimeoutSeconds { get; set; } = 300; // 5 minutes

        /// <summary>
        /// Whether to log detailed migration progress during startup
        /// </summary>
        public bool VerboseLogging { get; set; } = true;

        /// <summary>
        /// Whether to create a backup before applying startup migrations
        /// </summary>
        public bool BackupOnStartup { get; set; } = false;
    }
}
