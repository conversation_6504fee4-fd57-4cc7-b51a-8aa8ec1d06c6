# SfSignature Component Sizing Reference

## Overview
This document provides the correct approach for sizing the Syncfusion Blazor SfSignature component based on the official documentation.

## ❌ Incorrect Approach (Parameters)
```razor
<!-- These parameters DO NOT exist and will not work -->
<SfSignature Height="200px" Width="100%" />
```

## ✅ Correct Approach (CSS Styling)
```razor
<!-- Use the style attribute for sizing -->
<SfSignature style="width: 100%; height: 200px;" />
```

## Complete Example
```razor
<div class="signature-container border rounded p-2" style="background-color: #f8f9fa;">
    <SfSignature @ref="signatureComponent" 
               BackgroundColor="#ffffff"
               StrokeColor="#000000"
               MinStrokeWidth="1"
               MaxStrokeWidth="3"
               Velocity="0.7"
               style="width: 100%; height: 200px;">
    </SfSignature>
    <div class="d-flex justify-content-between mt-2">
        <button type="button" class="btn btn-outline-secondary btn-sm" @onclick="ClearSignature">
            <i class="bi bi-eraser"></i> Clear
        </button>
        <small class="text-muted align-self-center">Draw your signature above</small>
    </div>
</div>
```

## Available SfSignature Properties

### Stroke Customization
- `StrokeColor` - Color of the signature stroke (hex, RGB, or text)
- `MinStrokeWidth` - Minimum stroke width (number)
- `MaxStrokeWidth` - Maximum stroke width (number)
- `Velocity` - Controls stroke width variation (0.0 to 1.0)

### Background Customization
- `BackgroundColor` - Background color (hex, RGB, or text)
- `BackgroundImage` - Background image URL

### Sizing
- Use CSS `style` attribute for width and height
- Common patterns:
  - `style="width: 100%; height: 200px;"` - Full width, fixed height
  - `style="width: 400px; height: 300px;"` - Fixed dimensions
  - `style="width: 100%; height: 100%;"` - Fill parent container

## Best Practices

1. **Always use CSS styling** for dimensions, not component parameters
2. **Set explicit height** - signature pads need defined height to work properly
3. **Use percentage width** for responsive design
4. **Wrap in container** with proper styling for better visual presentation
5. **Test on mobile devices** to ensure touch functionality works correctly

## Common Issues

### Issue: Signature pad not visible
**Solution:** Ensure height is explicitly set via CSS style

### Issue: Signature pad too small on mobile
**Solution:** Use responsive CSS or media queries to adjust height for mobile devices

### Issue: Signature not capturing properly
**Solution:** Verify the component reference (@ref) is properly set and methods are called correctly

### Issue: Cursor position doesn't match drawing position in modal dialogs
**Problem:** When SfSignature is placed inside a modal/dialog, the cursor position doesn't align with where the signature is drawn. This is a known Syncfusion issue.

**Solution 1 - Conditional Rendering (Recommended):**
```razor
<SfDialog @bind-Visible="showModal">
    <DialogEvents Opened="OnModalOpened" OnClose="OnModalClosed"></DialogEvents>
    <DialogTemplates>
        <Content>
            @if (canRenderSignaturePad)
            {
                <SfSignature @ref="signatureComponent"
                           HtmlAttributes="@signatureHtmlAttributes">
                </SfSignature>
            }
            else
            {
                <div class="signature-loading">Loading signature pad...</div>
            }
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    protected bool canRenderSignaturePad = false;
    protected Dictionary<string, object> signatureHtmlAttributes = new()
    {
        { "width", "100%" },
        { "height", "200" },
        { "style", "width: 100%; height: 200px;" }
    };

    protected void OnModalOpened()
    {
        canRenderSignaturePad = true;
        StateHasChanged();
    }

    protected void OnModalClosed()
    {
        canRenderSignaturePad = false;
    }
}
```

**Solution 2 - HtmlAttributes Only:**
```razor
<SfSignature HtmlAttributes="@signatureHtmlAttributes"></SfSignature>

@code {
    private Dictionary<string, object> signatureHtmlAttributes = new()
    {
        { "width", "600" },
        { "height", "300" }
    };
}
```

## Reference
- [Syncfusion Blazor Signature Documentation](https://blazor.syncfusion.com/documentation/signature/customization)
- [Syncfusion Blazor Signature API Reference](https://help.syncfusion.com/cr/blazor/Syncfusion.Blazor.Inputs.SfSignature.html)
