using System;
using System.Collections.Generic;

namespace ShiningCMusicApi.Services.DatabaseMigration.Models
{
    /// <summary>
    /// Represents the result of a database migration operation
    /// </summary>
    public class MigrationResult
    {
        /// <summary>
        /// Indicates whether the migration operation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Overall message describing the migration result
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// List of scripts that were successfully applied
        /// </summary>
        public List<string> AppliedScripts { get; set; } = new();

        /// <summary>
        /// List of scripts that failed to apply
        /// </summary>
        public List<string> FailedScripts { get; set; } = new();

        /// <summary>
        /// Total time taken for the migration operation
        /// </summary>
        public TimeSpan TotalExecutionTime { get; set; }

        /// <summary>
        /// List of warnings encountered during migration
        /// </summary>
        public List<string> Warnings { get; set; } = new();

        /// <summary>
        /// Detailed results for each script processed
        /// </summary>
        public List<ScriptExecutionResult> ScriptResults { get; set; } = new();
    }

    /// <summary>
    /// Represents the result of executing a single script
    /// </summary>
    public class ScriptExecutionResult
    {
        /// <summary>
        /// Name of the script
        /// </summary>
        public string ScriptName { get; set; } = string.Empty;

        /// <summary>
        /// Whether the script executed successfully
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Execution time for this script
        /// </summary>
        public TimeSpan ExecutionTime { get; set; }

        /// <summary>
        /// Error message if the script failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Number of SQL batches executed
        /// </summary>
        public int BatchesExecuted { get; set; }

        /// <summary>
        /// Whether this was a new migration or an update to existing one
        /// </summary>
        public bool IsUpdate { get; set; }
    }
}
